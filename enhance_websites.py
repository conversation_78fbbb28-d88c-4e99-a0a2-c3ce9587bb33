# -*- coding: utf-8 -*-
"""
完善官网信息脚本
为QS Top100大学数据添加官方网站链接和完善国家信息
"""

import json
import logging
import re
from data_processor import DataProcessor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WebsiteEnhancer:
    """官网信息完善器"""

    def __init__(self):
        self.processor = DataProcessor()
        
        # 知名大学官网映射表
        self.university_websites = {
            'Massachusetts Institute of Technology (MIT)': 'https://web.mit.edu',
            'Imperial College London': 'https://www.imperial.ac.uk',
            'University of Oxford': 'https://www.ox.ac.uk',
            'Harvard University': 'https://www.harvard.edu',
            'University of Cambridge': 'https://www.cam.ac.uk',
            'Stanford University': 'https://www.stanford.edu',
            'ETH Zurich': 'https://ethz.ch',
            'National University of Singapore (NUS)': 'https://www.nus.edu.sg',
            'UCL': 'https://www.ucl.ac.uk',
            'University College London (UCL)': 'https://www.ucl.ac.uk',
            'California Institute of Technology (Caltech)': 'https://www.caltech.edu',
            'University of Pennsylvania': 'https://www.upenn.edu',
            'University of California, Berkeley (UCB)': 'https://www.berkeley.edu',
            'The University of Melbourne': 'https://www.unimelb.edu.au',
            'Peking University': 'https://www.pku.edu.cn',
            'Nanyang Technological University, Singapore (NTU Singapore)': 'https://www.ntu.edu.sg',
            'Cornell University': 'https://www.cornell.edu',
            'The University of Hong Kong': 'https://www.hku.hk',
            'The University of Sydney': 'https://www.sydney.edu.au',
            'The University of New South Wales (UNSW Sydney)': 'https://www.unsw.edu.au',
            'Tsinghua University': 'https://www.tsinghua.edu.cn',
            'University of Chicago': 'https://www.uchicago.edu',
            'Princeton University': 'https://www.princeton.edu',
            'Yale University': 'https://www.yale.edu',
            'Université PSL': 'https://psl.eu',
            'University of Toronto': 'https://www.utoronto.ca',
            'EPFL – École polytechnique fédérale de Lausanne': 'https://www.epfl.ch',
            'The University of Edinburgh': 'https://www.ed.ac.uk',
            'Technical University of Munich': 'https://www.tum.de',
            'McGill University': 'https://www.mcgill.ca',
            'Australian National University (ANU)': 'https://www.anu.edu.au',
            'Seoul National University': 'https://www.snu.ac.kr',
            'Johns Hopkins University': 'https://www.jhu.edu',
            'The University of Tokyo': 'https://www.u-tokyo.ac.jp',
            'Columbia University': 'https://www.columbia.edu',
            'The University of Manchester': 'https://www.manchester.ac.uk',
            'The Chinese University of Hong Kong (CUHK)': 'https://www.cuhk.edu.hk',
            'Monash University': 'https://www.monash.edu',
            'University of British Columbia': 'https://www.ubc.ca',
            'Fudan University': 'https://www.fudan.edu.cn',
            'King\'s College London': 'https://www.kcl.ac.uk',
            'University of California, Los Angeles (UCLA)': 'https://www.ucla.edu',
            'New York University (NYU)': 'https://www.nyu.edu',
            'University of Michigan-Ann Arbor': 'https://umich.edu',
            'Shanghai Jiao Tong University': 'https://www.sjtu.edu.cn',
            'The Hong Kong University of Science and Technology': 'https://www.ust.hk',
            'Zhejiang University': 'https://www.zju.edu.cn',
            'Delft University of Technology': 'https://www.tudelft.nl',
            'Kyoto University': 'https://www.kyoto-u.ac.jp',
            'Northwestern University': 'https://www.northwestern.edu',
            'The London School of Economics and Political Science (LSE)': 'https://www.lse.ac.uk',
            'KAIST - Korea Advanced Institute of Science & Technology': 'https://www.kaist.edu',
            'University of Bristol': 'https://www.bristol.ac.uk',
            'University of Amsterdam': 'https://www.uva.nl',
            'Yonsei University': 'https://www.yonsei.ac.kr',
            'The Hong Kong Polytechnic University': 'https://www.polyu.edu.hk',
            'Carnegie Mellon University': 'https://www.cmu.edu',
            'Ludwig-Maximilians-Universität München': 'https://www.lmu.de',
            'Universiti Malaya (UM)': 'https://www.um.edu.my',
            'University of Wisconsin-Madison': 'https://www.wisc.edu',
            'University of Illinois Urbana-Champaign': 'https://illinois.edu',
            'University of Washington': 'https://www.washington.edu',
            'KTH Royal Institute of Technology': 'https://www.kth.se',
            'University of Glasgow': 'https://www.gla.ac.uk',
            'University of Southampton': 'https://www.southampton.ac.uk',
            'University of Birmingham': 'https://www.birmingham.ac.uk',
            'University of Leeds': 'https://www.leeds.ac.uk',
            'University of Sheffield': 'https://www.sheffield.ac.uk',
            'University of Nottingham': 'https://www.nottingham.ac.uk',
            'Boston University': 'https://www.bu.edu',
            'University of St Andrews': 'https://www.st-andrews.ac.uk',
            'Lund University': 'https://www.lu.se',
            'Georgia Institute of Technology': 'https://www.gatech.edu',
            'Rice University': 'https://www.rice.edu',
            'University of Copenhagen': 'https://www.ku.dk',
            'University of Zurich': 'https://www.uzh.ch',
            'Trinity College Dublin, The University of Dublin': 'https://www.tcd.ie',
            'University of Oslo': 'https://www.uio.no',
            'University of Helsinki': 'https://www.helsinki.fi',
            'Pohang University of Science And Technology (POSTECH)': 'https://www.postech.ac.kr',
            'Durham University': 'https://www.durham.ac.uk',
            'Pennsylvania State University': 'https://www.psu.edu',
            'University of California, San Diego (UCSD)': 'https://ucsd.edu',
            'University of Auckland': 'https://www.auckland.ac.nz',
            'University of Texas at Austin': 'https://www.utexas.edu',
            'Sorbonne University': 'https://www.sorbonne-universite.fr',
            'University of Warwick': 'https://warwick.ac.uk',
            'Brown University': 'https://www.brown.edu',
            'University of Adelaide': 'https://www.adelaide.edu.au',
            'Nanjing University': 'https://www.nju.edu.cn',
            'Université catholique de Louvain (UCLouvain)': 'https://uclouvain.be',
            'University of Groningen': 'https://www.rug.nl',
            'Leiden University': 'https://www.universiteitleiden.nl',
            'University of Barcelona': 'https://www.ub.edu',
            'Universidad Autónoma de Madrid': 'https://www.uam.es',
            'Universitat Pompeu Fabra (UPF)': 'https://www.upf.edu',
            'Technische Universität Berlin (TU Berlin)': 'https://www.tu-berlin.de',
            'Freie Universität Berlin': 'https://www.fu-berlin.de',
            'Humboldt-Universität zu Berlin': 'https://www.hu-berlin.de',
            'Universität Hamburg': 'https://www.uni-hamburg.de',
            'Universität Heidelberg': 'https://www.uni-heidelberg.de',
            'Universität Freiburg': 'https://uni-freiburg.de',
            'Universität Göttingen': 'https://www.uni-goettingen.de',
            'Universität Tübingen': 'https://uni-tuebingen.de',
            'Universität Bonn': 'https://www.uni-bonn.de',
            'Universität Münster': 'https://www.uni-muenster.de'
        }
        
        # 国家信息映射表
        self.country_mapping = {
            'Massachusetts Institute of Technology (MIT)': 'United States',
            'Imperial College London': 'United Kingdom',
            'University of Oxford': 'United Kingdom',
            'Harvard University': 'United States',
            'University of Cambridge': 'United Kingdom',
            'Stanford University': 'United States',
            'ETH Zurich': 'Switzerland',
            'National University of Singapore (NUS)': 'Singapore',
            'UCL': 'United Kingdom',
            'University College London (UCL)': 'United Kingdom',
            'California Institute of Technology (Caltech)': 'United States',
            'University of Pennsylvania': 'United States',
            'University of California, Berkeley (UCB)': 'United States',
            'The University of Melbourne': 'Australia',
            'Peking University': 'China',
            'Nanyang Technological University, Singapore (NTU Singapore)': 'Singapore',
            'Cornell University': 'United States',
            'The University of Hong Kong': 'Hong Kong',
            'The University of Sydney': 'Australia',
            'The University of New South Wales (UNSW Sydney)': 'Australia',
            'Tsinghua University': 'China',
            'University of Chicago': 'United States',
            'Princeton University': 'United States',
            'Yale University': 'United States',
            'Université PSL': 'France',
            'University of Toronto': 'Canada',
            'EPFL – École polytechnique fédérale de Lausanne': 'Switzerland',
            'The University of Edinburgh': 'United Kingdom',
            'Technical University of Munich': 'Germany',
            'McGill University': 'Canada',
            'Australian National University (ANU)': 'Australia',
            'Seoul National University': 'South Korea',
            'Johns Hopkins University': 'United States',
            'The University of Tokyo': 'Japan',
            'Columbia University': 'United States',
            'The University of Manchester': 'United Kingdom',
            'The Chinese University of Hong Kong (CUHK)': 'Hong Kong',
            'Monash University': 'Australia',
            'University of British Columbia': 'Canada',
            'Fudan University': 'China',
            'King\'s College London': 'United Kingdom'
        }

    def enhance_data(self, input_file="qs_top100_basic_data.json"):
        """完善大学数据"""
        try:
            # 读取基础数据
            with open(input_file, 'r', encoding='utf-8') as f:
                universities = json.load(f)
            
            logger.info(f"读取了 {len(universities)} 所大学的基础数据")
            
            # 完善官网信息
            website_enhanced = self._enhance_websites(universities)
            
            # 完善国家和洲际信息
            country_enhanced = self._enhance_countries(universities)
            
            # 添加洲际信息
            continent_enhanced = self._add_continents(universities)
            
            # 保存完善后的数据
            output_file = "qs_top100_complete.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(universities, f, ensure_ascii=False, indent=2)
            
            logger.info(f"完善后的数据已保存到 {output_file}")
            
            # 显示统计信息
            self._show_statistics(universities, website_enhanced, country_enhanced, continent_enhanced)
            
            return universities, output_file
            
        except FileNotFoundError:
            logger.error(f"找不到输入文件: {input_file}")
            logger.error("请先运行 python qs_top100_scraper.py 生成基础数据")
            return None, None
        except Exception as e:
            logger.error(f"完善数据时出错: {e}")
            return None, None

    def _enhance_websites(self, universities):
        """完善官网信息"""
        enhanced_count = 0
        
        for uni in universities:
            name = uni['name']
            
            # 直接匹配
            if name in self.university_websites:
                uni['official_website'] = self.university_websites[name]
                enhanced_count += 1
            else:
                # 模糊匹配
                matched_website = self._fuzzy_match_website(name)
                if matched_website:
                    uni['official_website'] = matched_website
                    enhanced_count += 1
        
        logger.info(f"完善了 {enhanced_count} 所大学的官网信息")
        return enhanced_count

    def _enhance_countries(self, universities):
        """完善国家信息"""
        enhanced_count = 0
        
        for uni in universities:
            name = uni['name']
            
            # 如果国家信息缺失或为空
            if not uni.get('country'):
                if name in self.country_mapping:
                    uni['country'] = self.country_mapping[name]
                    enhanced_count += 1
                else:
                    # 尝试模糊匹配
                    matched_country = self._fuzzy_match_country(name)
                    if matched_country:
                        uni['country'] = matched_country
                        enhanced_count += 1
        
        logger.info(f"完善了 {enhanced_count} 所大学的国家信息")
        return enhanced_count

    def _add_continents(self, universities):
        """添加洲际信息"""
        enhanced_count = 0
        
        for uni in universities:
            country = uni.get('country', '')
            if country:
                continent = self.processor.get_continent_by_country(country)
                uni['continent'] = continent
                enhanced_count += 1
            else:
                uni['continent'] = 'Unknown'
        
        logger.info(f"添加了 {enhanced_count} 所大学的洲际信息")
        return enhanced_count

    def _fuzzy_match_website(self, university_name):
        """模糊匹配官网"""
        for known_name, website in self.university_websites.items():
            if self._names_similar(university_name, known_name):
                logger.info(f"模糊匹配官网: {university_name} -> {known_name}")
                return website
        return ""

    def _fuzzy_match_country(self, university_name):
        """模糊匹配国家"""
        for known_name, country in self.country_mapping.items():
            if self._names_similar(university_name, known_name):
                logger.info(f"模糊匹配国家: {university_name} -> {country}")
                return country
        return ""

    def _names_similar(self, name1, name2):
        """检查两个大学名称是否相似"""
        # 提取主要词汇
        words1 = set(re.findall(r'\w+', name1.lower()))
        words2 = set(re.findall(r'\w+', name2.lower()))
        
        # 移除常见词汇
        common_words = {'university', 'college', 'institute', 'school', 'of', 'the', 'and', 'technology'}
        words1 -= common_words
        words2 -= common_words
        
        # 计算交集
        intersection = words1 & words2
        union = words1 | words2
        
        if not union:
            return False
        
        # 如果交集占比超过60%，认为相似
        similarity = len(intersection) / len(union)
        return similarity > 0.6

    def _show_statistics(self, universities, website_enhanced, country_enhanced, continent_enhanced):
        """显示统计信息"""
        logger.info("\n=== 数据完善统计 ===")
        
        total_count = len(universities)
        websites_count = sum(1 for uni in universities if uni.get('official_website'))
        countries_count = sum(1 for uni in universities if uni.get('country'))
        continents_count = sum(1 for uni in universities if uni.get('continent') and uni['continent'] != 'Unknown')
        
        logger.info(f"总大学数量: {total_count}")
        logger.info(f"官网信息: {websites_count}/{total_count} ({websites_count/total_count*100:.1f}%)")
        logger.info(f"国家信息: {countries_count}/{total_count} ({countries_count/total_count*100:.1f}%)")
        logger.info(f"洲际信息: {continents_count}/{total_count} ({continents_count/total_count*100:.1f}%)")
        
        # 各洲际分布
        continent_count = {}
        for uni in universities:
            continent = uni.get('continent', 'Unknown')
            continent_count[continent] = continent_count.get(continent, 0) + 1
        
        logger.info("\n各洲际分布:")
        for continent, count in sorted(continent_count.items()):
            logger.info(f"  {continent}: {count} 所")

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("QS Top100 官网信息完善工具")
    logger.info("=" * 60)
    
    enhancer = WebsiteEnhancer()
    universities, output_file = enhancer.enhance_data()
    
    if universities and output_file:
        print(f"\n✅ 数据完善完成！")
        print(f"📁 完整数据已保存到: {output_file}")
        print(f"📊 总共处理了 {len(universities)} 所大学的数据")
        
        # 显示前10名验证
        print(f"\n🔍 前10名大学验证:")
        for uni in universities[:10]:
            website_status = "✅" if uni.get('official_website') else "❌"
            print(f"  {uni['qs_ranking']}. {uni['name']} - {website_status}")
    else:
        print("❌ 数据完善失败")

if __name__ == "__main__":
    main()
